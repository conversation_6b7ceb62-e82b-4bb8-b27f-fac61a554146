package com.voghion.marketing.admin.controller;


import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.marketing.core.PopoverConfigCoreService;
import com.voghion.marketing.model.vo.PopoverCondition;
import com.voghion.marketing.model.vo.PopoverConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@RestController
@RequestMapping("/popover")
@Api(tags = "弹窗配置")
public class PopoverConfigController {

    @Resource
    private PopoverConfigCoreService popoverConfigCoreService;

    @ApiOperation("新增弹窗配置")
    @PostMapping("add")
    public Result<Boolean> addPopover(@RequestBody PopoverConfigVO popoverConfigVO) {
        popoverConfigCoreService.addPopover(popoverConfigVO);
        return Result.success(true);
    }

    @ApiOperation("修改弹窗配置")
    @PostMapping("update")
    public Result<Boolean> updatePopover(@RequestBody PopoverConfigVO popoverConfigVO) {
        popoverConfigCoreService.updatePopover(popoverConfigVO);
        return Result.success(true);
    }

    @ApiOperation("修改弹窗权重")
    @PostMapping("updateSort")
    public Result<Boolean> updatePopoverSort(@RequestBody PopoverConfigVO popoverConfigVO) {
        popoverConfigCoreService.updatePopoverSort(popoverConfigVO);
        return Result.success(true);
    }

    @ApiOperation("修改弹窗展示隐藏")
    @PostMapping("updateShow")
    public Result<Boolean> updatePopoverShow(@RequestBody PopoverConfigVO popoverConfigVO) {
        popoverConfigCoreService.updatePopoverShow(popoverConfigVO);
        return Result.success(true);
    }

    @ApiOperation("删除弹窗配置")
    @DeleteMapping("delete/{id}")
    public Result<Boolean> deletePopover(@PathVariable Long id) {
        popoverConfigCoreService.deletePopover(id);
        return Result.success(true);
    }

    @ApiOperation("获取弹窗配置")
    @PostMapping("list")
    public Result<PageView<PopoverConfigVO>> queryPopover(@RequestBody PopoverCondition popoverCondition) {
        return Result.success(popoverConfigCoreService.queryPageByCondition(popoverCondition));
    }

    @ApiOperation("获取弹窗配置")
    @PostMapping("queryTouchPoint")
    public Result<List<PopoverConfigVO>> queryTouchPoint(@RequestBody PopoverCondition popoverCondition) {
        return Result.success(popoverConfigCoreService.queryTouchPoint(popoverCondition));
    }

    @ApiOperation("查询版本号")
    @PostMapping("queryAppVersion")
    public Result<List<String>> queryAppVersion(@RequestBody PopoverCondition popoverCondition) {
        return Result.success(popoverConfigCoreService.queryAppVersion(popoverCondition));
    }
}
