package com.voghion.marketing.admin.controller;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.marketing.core.CouponCoreService;
import com.voghion.marketing.core.MerchantCouponService;
import com.voghion.marketing.enums.CouponBaseTypeEnums;
import com.voghion.marketing.exception.MarketingBizException;
import com.voghion.marketing.model.po.MerchantNewUserCouponOperationRecord;
import com.voghion.marketing.model.vo.MerchantAddCouponVO;
import com.voghion.marketing.model.vo.MerchantCouponQueryVO;
import com.voghion.marketing.model.vo.MerchantNewUserCouponVO;
import com.voghion.marketing.model.vo.MerchantStatusUpdateVO;
import com.voghion.marketing.model.vo.coupon.MerchantCouponVO;
import com.voghion.marketing.model.vo.coupon.MerchantNewUserCouponInfo;
import com.voghion.marketing.utils.RedisUtil;
import com.voghion.order.model.OrderCouponVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/merchant/coupon")
@Api(tags = "商家优惠优惠券")
@Slf4j
public class MerchantCouponController {

    @Resource
    private CouponCoreService couponCoreService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private MerchantCouponService merchantCouponService;

    @PostMapping(value = "/update/new/user")
    @ApiOperation(value = "商家修改新人优惠券", notes = "商家新增或者修改新人优惠券")
    public Result<Integer> updateNewUserCoupon(@RequestBody @Valid @NotNull MerchantNewUserCouponVO merchantNewUserCouponVO) {
        log.info("[MerchantCouponController] addCoupon, couponVO ={}", merchantNewUserCouponVO);
        Integer coupon = null;
        try {
            coupon = merchantCouponService.addOrUpdateMerchantNewUserCoupon(merchantNewUserCouponVO);
        } catch (MarketingBizException marketingException) {
            log.error("[addNewUserCoupon] 业务异常, error = {}", marketingException.getErrorMessage());
            return Result.fail(marketingException.getErrorCode(), marketingException.getErrorMessage());
        } catch (Exception e) {
            log.error("[addNewUserCoupon] 系统异常, error = {}", e.getMessage(), e);
            return Result.fail("SYSTEM_ERROR", "系统异常");
        }
        return Result.success(coupon);
    }

    @GetMapping(value = "/query/new/user/operation/record")
    @ApiOperation(value = "查询商家新人优惠券操作记录", notes = "查询商家新人优惠券操作记录")
    public Result<List<MerchantNewUserCouponOperationRecord>> queryNewUserOperationRecord(@RequestParam("shopId") Long shopId,
                                                                                          @RequestParam("couponId") Long couponId) {
        if (Objects.isNull(shopId) || shopId < 1) {
            log.error("[queryNewUserOperationRecord] 参数错误，shopId以及couponId不能为空");
            return Result.fail("PARAM_ERROR", "参数错误");
        }
        log.info("[MerchantCouponController] queryNewUserOperationRecord, couponId ={}, shopId = {}", couponId, shopId);
        return Result.success(merchantCouponService.queryNewUserRecords(shopId, couponId));
    }

    @GetMapping(value = "/query/new/user/info")
    @ApiOperation(value = "新人券查询", notes = "新人优惠券查询")
    public Result<MerchantNewUserCouponInfo> queryNewUserCouponInfo(@RequestParam("shopId") Long shopId,
                                                                    @RequestParam("shopName") String shopName) {
        log.info("[MerchantCouponController] queryNewUserCouponInfo, shopId = {}, shopName = {}", shopId, shopName);
        if (Objects.isNull(shopId) || shopId < 1) {
            log.error("[queryNewUserCouponInfo] 参数错误，shopId不能为空");
            return Result.fail("PARAM_ERROR", "参数错误");
        }
        MerchantNewUserCouponInfo userCouponInfo = merchantCouponService.queryNewUserCouponInfo(shopId, shopName);
        return Result.success(userCouponInfo);
    }

    @GetMapping(value = "/query/orders")
    @ApiOperation(value = "新人券订单列表查询", notes = "新人券订单列表查询")
    public Result<PageView<OrderCouponVO>> queryNewUserCouponOrders(@RequestParam("shopId") Long shopId,
                                                                    @RequestParam("couponId") Long couponId,
                                                                    @RequestParam("orderId") String orderId,
                                                                    @RequestParam("payStartTime") String startTime,
                                                                    @RequestParam("payEndTime") String endTime,
                                                                    @RequestParam("pageSize") Integer pageSize,
                                                                    @RequestParam("pageNum") Integer pageNum) {
        log.info("[MerchantCouponController] queryNewUserCouponOrders, shopId = {}, couponId = {}, orderId = {}, " +
                "startTime = {}, endTime = {}, pageSize = {}, pageNum = {}", shopId, couponId, orderId, startTime, endTime, pageSize, pageNum);
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 0) {
            pageSize = 10;
        }
        return merchantCouponService.queryOrderList(couponId, orderId, startTime, endTime, pageSize, pageNum);
    }

    /**
     * {@link  MerchantCouponQueryVO}
     *
     * @return
     */
    @GetMapping(value = "/page/query")
    @ApiOperation(value = "店铺查询优惠券列表", notes = "店铺查询优惠券列表")
    public Result<PageView<MerchantCouponVO>> pageQueryCoupons(@RequestParam("shopId") Long shopId,
                                                               @RequestParam("couponId") Long couponId,
                                                               @RequestParam("couponName") String couponName,
                                                               @RequestParam("couponType") Integer couponType,
                                                               @RequestParam("productId") Long productId,
                                                               @RequestParam("status") Integer status,
                                                               @RequestParam("pageNum") Integer pageNum,
                                                               @RequestParam("pageSize") Integer pageSize) {
        log.info("[MerchantCouponController] pageQueryCoupons, shopId = {}, couponId ={}, couponName = {}, couponType = {}, productId = {}, " +
                "status = {}, pageNum = {}, pageSize = {}", shopId, couponId, couponName, couponType, productId, status, pageNum, pageSize);
        if (shopId == null) {
            log.error("[pageQueryCoupons] 参数错误，shopId不能为空");
            return Result.fail("PARAM_ERROR", "参数错误");
        }
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 0) {
            pageSize = 10;
        }
        PageView<MerchantCouponVO> pageView = merchantCouponService.pageQuery(shopId, null, couponId, couponName,
                couponType, CouponBaseTypeEnums.USUAL_COUPON.getCode(), productId, status, pageNum, pageSize);
        return Result.success(pageView);
    }


    @PostMapping(value = "/upsert")
    @ApiOperation(value = "店铺新增或者修改优惠券", notes = "店铺新增或者修改优惠券")
    public Result addOrUpdateMerchantCoupon(@RequestBody @Valid @NotNull MerchantAddCouponVO merchantAddCouponVO) {
        log.info("[MerchantCouponController] addOrUpdateMerchantCoupon, merchantCouponQueryVO ={}", merchantAddCouponVO);
        try {
            merchantCouponService.addOrUpdateMerchantCoupon(merchantAddCouponVO);
        } catch (MarketingBizException marketingException) {
            log.error("[addOrUpdateMerchantCoupon] 业务异常, error = {}", marketingException.getErrorMessage());
            return Result.fail(marketingException.getErrorCode(), marketingException.getErrorMessage());
        } catch (Exception e) {
            log.error("[addOrUpdateMerchantCoupon] 系统异常, error = {}", e.getMessage(), e);
            return Result.fail("SYSTEM_ERROR", "系统异常");
        }
        return Result.success(null);
    }

    @PostMapping(value = "/update/status")
    @ApiOperation(value = "修改店铺优惠券状态", notes = "修改店铺优惠券状态")
    public Result updateStatus(@RequestBody @Valid @NotNull MerchantStatusUpdateVO merchantStatusUpdateVO) {
        log.info("[MerchantCouponController] updateStatus, merchantStatusUpdateVO = {}", merchantStatusUpdateVO);
        try {
            merchantCouponService.updateCouponStatus(merchantStatusUpdateVO);
        } catch (MarketingBizException marketingException) {
            log.error("[updateStatus] 业务异常, error = {}", marketingException.getErrorMessage());
            return Result.fail(marketingException.getErrorCode(), marketingException.getErrorMessage());
        } catch (Exception e) {
            log.error("[updateStatus] 系统异常, error = {}", e.getMessage(), e);
            return Result.fail("SYSTEM_ERROR", "系统异常");
        }
        return Result.success(null);
    }

    @GetMapping(value = "/page/query/all/merchants")
    @ApiOperation(value = "店铺查询优惠券列表", notes = "店铺查询优惠券列表")
    public Result<PageView<MerchantCouponVO>> pageQueryAllCoupons(@RequestParam("couponId") Long couponId,
                                                                  @RequestParam("couponName") String couponName,
                                                                  @RequestParam("shopName") String shopName,
                                                                  @RequestParam("productId") Long productId,
                                                                  @RequestParam("baseType") Integer baseType,
                                                                  @RequestParam("status") Integer status,
                                                                  @RequestParam("pageNum") Integer pageNum,
                                                                  @RequestParam("pageSize") Integer pageSize) {
        log.info("[MerchantCouponController] pageQueryAllCoupons, couponId ={}, couponName = {}, shopName = {}, productId = {}, baseType = {} " +
                "status = {}, pageNum = {}, pageSize = {}", couponId, couponName, shopName, productId, baseType, status, pageNum, pageSize);
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 0) {
            pageSize = 10;
        }
        PageView<MerchantCouponVO> pageView = merchantCouponService.pageQuery(null, shopName, couponId, couponName,
                null, baseType, productId, status, pageNum, pageSize);
        return Result.success(pageView);
    }
}
