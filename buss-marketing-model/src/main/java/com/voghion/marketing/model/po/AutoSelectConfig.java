package com.voghion.marketing.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 自动审核入选配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AutoSelectConfig extends Model<AutoSelectConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1 - flashdeal 2 - 运营选品 4满减
     */
    private Integer type;

    /**
     * 入选类型id
     */
    private Long typeId;

    /**
     * 商品名称相同是否只入选最低价 0 - 否 1 - 是
     */
    private Integer isSameName;

    /**
     * 动销条件 几日内
     */
    private Integer runDays;

    /**
     * 动销条件 大于等于几单
     */
    private Integer sales;

    /**
     * 符合一个动销商品额外报名数量
     */
    private Integer overGoodsNum = 0;

    /**
     *在规则限制之外额外允许报名数量
     */
    private Integer overLimitNum = 0;

    /**
     * 是否负向商品 1 - 是 0 - 否
     */
    private Integer isNegative;

    /**
     * 均价限制  0 - 不应用 1 - 应用
     */
    private Integer averagePriceCheck;

    /**
     * 相同前百分比
     */
    private BigDecimal sameTop;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 商家白名单列表
     */
    private String whiteShopList;

    /**
     * 商家店铺的白名单标签，逗号隔开。
     */
    private String whiteShopTagList;

    @ApiModelProperty(value = "商品活动价小于N天单价 0 7 15 30 60 90")
    private Integer goodsActivityPriceDays;

    @ApiModelProperty(value = "商品创建时间 近7 15 30 60 90 180 360 天")
    private Integer goodsCreateDays;

    @ApiModelProperty(value = "商品标签")
    private String goodsTagIdList;

    @ApiModelProperty(value = "商品活动价是否低于活动建议价格 0 否 1 是")
    private Integer isActivityPriceLow;

    @ApiModelProperty(value = "售卖区域")
    private String saleAreaList;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
