package com.voghion.marketing.model.vo.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.voghion.marketing.model.vo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AvailGoodsForShopVO {

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "活动id")
    private Long ActivityId;

    @ApiModelProperty(value = "商品id")
    private Long goodsId;

    @ApiModelProperty(value = "商品主图")
    private String mainImage;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品最小销售价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "商品最大销售价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "商品销售区域")
    private String saleArea;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "是否选中")
    private boolean checked = false;
}
