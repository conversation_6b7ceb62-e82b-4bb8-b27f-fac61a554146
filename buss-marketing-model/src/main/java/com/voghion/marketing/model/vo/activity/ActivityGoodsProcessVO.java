package com.voghion.marketing.model.vo.activity;

import lombok.Data;

/**
 * 首页展示配置
 * <AUTHOR>
 * @Date 2025/07/29
 */
@Data
public class ActivityGoodsProcessVO {

    private Long activityId;

    //待处理商品数量
    private Integer firstPendingGoodsCount = 0;

    //总商品数量
    private Integer firstTotalGoodsCount = 0;

    //待处理商品数量
    private Integer lastPendingGoodsCount = 0;

    //总商品数量
    private Integer lastTotalGoodsCount = 0;
}
