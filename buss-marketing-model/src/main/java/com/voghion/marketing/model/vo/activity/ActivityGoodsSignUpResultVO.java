package com.voghion.marketing.model.vo.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @date: 2025/5/29
 * @author: longwei
 */
@Data
public class ActivityGoodsSignUpResultVO {

    private List<ActivityGoodsSignUpResult> resultList;

    @Data
    public static class ActivityGoodsSignUpResult {

        @ApiModelProperty("商品id")
        private Long goodsId;

        @ApiModelProperty("商家id")
        private Long shopId;

        @ApiModelProperty("操作结果 1报名成功 2不存在或已删除 3参与了其他活动 4商品已下架 5不满足活动类目要求 6不是限定商家")
        private Integer code;

        @ApiModelProperty("额外信息")
        private String extra;
    }
}
