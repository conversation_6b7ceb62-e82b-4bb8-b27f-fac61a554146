package com.voghion.marketing.model.vo.activity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voghion.marketing.model.conveter.LocalDateTimeConverter;
import com.voghion.marketing.model.conveter.LocalDateTimeDeSerializerConverter;
import com.voghion.marketing.model.po.NewActivityGoods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityGoodsVO extends NewActivityGoods {
    private static final long serialVersionUID = 1089595859102447739L;

    private Long id;

    private List<Long> activityIds;

    private Integer pageNow = 1;

    private Integer pageSize = 20;

    private String mainImage;

    private BigDecimal discount;

    private BigDecimal price;


    /**
     * 0未开始    1 报名中  2 已到期
     */
    private Integer signUpStatus;

    /**
     * 终审状态 1入选  0 落选  -1 待审核
     */
    private Integer status;

    private Boolean isApp = false;

    private Long searchCategoryId;

    private List<Long> searchCategoryIds;

    private BigDecimal searchRealMinPrice;

    private BigDecimal searchRealMaxPrice;

    /**
     * 初审状态  1入选  0 落选  -1 待审核
     */
    private Integer firstStatus;

    /**
     * 初审审核人
     */
    private String firstAuditor;

    /**
     * 商品id
     */
   private  Long shopId;
    /**
     * 终审人
     */
   private String operator;

    @ApiModelProperty("起始(最低拼团价)")
    private BigDecimal startMinGrouponPrice;


   private Integer sort;

    /**
     * 商品id,隔开
     */
   private String goodsIds;


   private List<Integer> goodsIdList;

   private boolean merchant;

    /**
     * 负责小二名称
     */
   private String principal;

    /**
     * 店铺id列表
     */
   private List<Long> shopIdList;

    @Override
    public String getMainImage() {
        return mainImage;
    }

    @ApiModelProperty("终止(最低拼团价)")
    private BigDecimal endMinGrouponPrice;

    @ApiModelProperty("起始(最高拼团价)")
    private BigDecimal startMaxGrouponPrice;

    @ApiModelProperty("终止(最高拼团价)")
    private BigDecimal endMaxGrouponPrice;

    /**
     * 机审状态 0 - 未通过 1 - 通过
     */
    @ApiModelProperty("机审状态 0 - 未通过 1 - 通过")
    private Integer autoSelectStatus;

    // 是否店铺查询
    private boolean isShop;

    @ApiModelProperty("失败原因")
    private String autoSelectFailReason;

    @ApiModelProperty("创建时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    private LocalDateTime goodsCreateTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    @ApiModelProperty("创建时间结束")
    private LocalDateTime goodsCreateTimeEnd;

    @ApiModelProperty("商品状态")
    private String isShow;

    @ApiModelProperty("折扣开始")
    private BigDecimal discountStart;
    @ApiModelProperty("折扣结束")
    private BigDecimal discountEnd;

    private Integer type;

    private String shopName;

    @ApiModelProperty("活动开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    private LocalDateTime activityStartTime;

    @ApiModelProperty("活动结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    private LocalDateTime activityEndTime;

    @ApiModelProperty("活动报名开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    private LocalDateTime signUpStartTime;

    @ApiModelProperty("活动报名结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    private LocalDateTime signUpEndTime;


    @ApiModelProperty("生效时间点")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    @JsonSerialize(using = LocalDateTimeConverter.class)
    private LocalDateTime effectTime;

    @ApiModelProperty("活动状态 1未开始 2 进行中 3已结束")
    private Integer activityStatus;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("是否查询历史数据")
    private Integer isHistory;

    public Boolean getIsApp() {
        return null == isApp ? false : isApp;
    }

    public ActivityGoodsVO(Long activityId, Integer status, Integer pageNow, Integer pageSize, Boolean isApp) {
        setActivityId(activityId);
        setStatus(status);
        this.pageNow = pageNow;
        this.pageSize = pageSize;
        this.isApp = isApp;
    }

    public ActivityGoodsVO() {
    }
}
