package com.voghion.marketing.model.vo.activity;

import com.voghion.marketing.model.vo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AvailGoodsForShopQueryVO extends PageParam {

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "活动id")
    private Long ActivityId;

    @ApiModelProperty(value = "商品id列表")
    private List<Long> goodsIdList;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "null - 全部， true - 已选， false - 未选")
    private Boolean checked;

}
