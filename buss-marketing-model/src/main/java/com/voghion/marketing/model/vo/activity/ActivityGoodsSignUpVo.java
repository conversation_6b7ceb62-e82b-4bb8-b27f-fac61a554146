package com.voghion.marketing.model.vo.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @date: 2022/3/5 15:03
 * @author: jashley
 */
@Data
public class ActivityGoodsSignUpVo implements Serializable {
    private static final long serialVersionUID = 7534110607157166861L;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("商品id(多个id以,分割)")
    private String goodsIds;

    @ApiModelProperty("折扣")
    private BigDecimal discount;

    private String userName;
}
