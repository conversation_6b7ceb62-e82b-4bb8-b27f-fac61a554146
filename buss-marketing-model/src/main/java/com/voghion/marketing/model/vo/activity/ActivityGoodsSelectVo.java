package com.voghion.marketing.model.vo.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @date: 2022/3/5 15:03
 * @author: jashley
 */
@Data
public class ActivityGoodsSelectVo implements Serializable {
    private static final long serialVersionUID = 7534110607157166861L;

    @ApiModelProperty("1入选/0落选")
    private Integer select;

    @ApiModelProperty("活动商品主键id")
    private List<Long> ids;

    @ApiModelProperty("驳回备注")
    private String remark;
}
