package com.voghion.marketing.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * PopoverCondition
 *
 * <AUTHOR>
 * @date 2023/3/31
 */
@ApiModel
@Data
public class PopoverConfigVO extends PageParam implements Serializable {
    private static final long serialVersionUID = -1198519476424443823L;
    /**
     * 主键id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 展示国家
     */
    @ApiModelProperty("展示国家")
    private String country;

    /**
     * 展示国家集合
     */
    @ApiModelProperty("展示国家")
    private List<String> countryList;

    /**
     * 用户分组
     */
    @ApiModelProperty(value = "用户分组")
    private String deviceGroup;


    @ApiModelProperty("ios版本")
    private List<String> iosVersionList;

    @ApiModelProperty("ios版本")
    private String iosVersion;

    @ApiModelProperty("android版本")
    private List<String> androidVersionList;

    @ApiModelProperty("android版本")
    private String androidVersion;

    @ApiModelProperty("是否互斥 0否 1是")
    private Integer isExclusive;


    /**
     * 用户分组
     */
    @ApiModelProperty(value = "用户分组")
    private List<String> deviceGroupList;

    /**
     * 1领券 2营销 3通知 4利益点 5支付利益点 6通知 7更新 8批发
     */
    @ApiModelProperty(value = "1领券 2营销 3通知 4利益点 5支付利益点 6通知 7更新 8批发")
    private Integer type;

    /**
     * 入口标题
     */
    @ApiModelProperty(value = "入口标题")
    private String entryTitle;
    /**
     * 文本标题
     */
    @ApiModelProperty(value = "文本标题")
    private String textTitle;
    /**
     * 文本正文
     */
    @ApiModelProperty(value = "文本正文")
    private String textContent;
    /**
     * 图标图
     */
    @ApiModelProperty(value = "图标图")
    private String iconImg;

    /**
     * 资源图
     */
    @ApiModelProperty(value = "资源图")
    private String backgroundImg;

    /**
     * 图标类型
     */
    @ApiModelProperty(value = "图标类型")
    private Integer iconType;

    /**
     * 路由value
     */
    @ApiModelProperty(value = "路由value")
    private String value;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    /**
     * 位置 1首页 2类目页 3类目商品页 4搜索页 5会场页 6ME页面 7购物车 8checkout 9支付渠道
     */
    @ApiModelProperty(value = "1首页 2类目页 3类目商品页 4搜索页 5会场页 6ME页面 7购物车 8checkout 9支付渠道 10")
    private String position;

    /**
     * 1首页 2类目页 3类目商品页 4搜索页 5会场页 6ME页面 7购物车 8checkout 9支付渠道
     */
    @ApiModelProperty("1首页 2类目页 3类目商品页 4搜索页 5会场页 6ME页面 7购物车 8checkout 9支付渠道")
    private List<Integer> positionList;

    /**
     * 频率
     */
    @ApiModelProperty(value = "频率")
    private Integer frequency;

    /**
     * 是否显示 0否 1是
     */
    @ApiModelProperty(value = "是否显示 0否 1是")
    private Integer isShow;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime effectStartTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime effectEndTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后修改人")
    private String updateUser;

    /**
     *  1弹窗 2通知
     */
    @ApiModelProperty(value = "1弹窗 2通知")
    private Integer flag = 1;

    /**
     * 支付渠道 1,Card 2,Paypal3,Klarna4,Pay in 45,Pay Later6,4X PayPal7,Später Bezahlen
     */
    @ApiModelProperty(value = "支付渠道 1,Card 2,Paypal3,Klarna4,Pay in 45,Pay Later6,4X PayPal7,Später Bezahlen")
    private Integer payment;

    @ApiModelProperty(value = "批发最大数量")
    private Long wholesaleMaxLimit;
}
