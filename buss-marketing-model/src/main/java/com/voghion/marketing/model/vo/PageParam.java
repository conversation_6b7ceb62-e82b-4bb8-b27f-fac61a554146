/*
 * Copyright (c) 2018-2999 广州亚米信息科技有限公司 All rights reserved.
 *
 * https://www.gz-yami.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.voghion.marketing.model.vo;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.io.Serializable;

/**
 *  <AUTHOR>
 *  @time 2021/3/26 11:28
 *  @describe 
*/
@Data
public class PageParam  implements Serializable {

    private String orderby;

    /**
     * 每页显示条数，默认 10
     */
    @ApiParam(value = "每页大小，默认10",required = false)
    private long pageSize = 10;

    /**
     * 当前页
     */
    @ApiParam(value = "当前页，默认1",required = false)
    private long pageNow = 1;


}
