package com.voghion.marketing.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class MerchantNewUserCouponVO {


    @ApiModelProperty(value = "优惠券id")
    @NotNull(message = "优惠券id不能为null")
    private Long couponId;

    /**
     * 减免金额
     */
    @ApiModelProperty(value = "减免金额", required = true)
    @NotNull(message = "减免金额不能为null")
    private BigDecimal reduceAmount;

    /**
     * 状态，20开启，30关闭
     */
    @ApiModelProperty(value = "状态;20开启，30 关闭", required = true)
    @NotNull(message = "状态不能为null")
    private int status;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id", required = true)
    @NotNull(message = "店铺id不能为null")
    private Long shopId;

    /**
     * 店铺name
     */
    @ApiModelProperty(value = "店铺名称", required = true)
    @NotNull(message = "店铺名称不能为null")
    private String shopName;
}
