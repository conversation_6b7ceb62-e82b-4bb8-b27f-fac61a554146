package com.voghion.marketing.model.vo.activity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.colorlight.base.model.PageView;
import com.voghion.marketing.model.po.NewActivityConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ActivityVO  extends PageView {
    private static final long serialVersionUID = -3219552225892373186L;
    private List<String> cateIdTree;

    private Integer isAll;

    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动状态：0-全部 10-待开始 20-进行中 30-已暂停 40-已过期，default=0
     */
    private Integer activityStatus;

    /**
     * 0未结算 1已结束
     */
    private Integer isEnd;

    /**
     * id 名称搜索
     */
    private String content;

    /**
     * 限定类目类型：0-不限制 1-限定类目 2-排除类目，default=0
     */
    private Integer catType;

    /**
     * 后台类目集合（,分开）
     */
    private String cateCol;

    /**
     * 开始报名时间
     */
    private String signStartTime;

    /**
     * 报名结束时间
     */
    private String signEndTime;

    /**
     * 开始生效时间
     */
    private String effectStartTime;

    /**
     * 结束时间
     */
    private String effectEndTime;

    /**
     * 最低折扣
     */
    private BigDecimal lowestDiscount;

    /**
     * App展示最低折扣
     */
    private BigDecimal showDiscountMin;

    /**
     * App展示最高折扣
     */
    private BigDecimal showDiscountMax;

    /**
     * 可报名数量
     */
    private Long signUpTotal;

    private String createTime;

    private String updateTime;


    private String operator;

    private Integer isDel;

    @TableField(exist = false)
    private Integer status;


    private Integer signUpStatus;

    private Integer number;


    private String labelImg;


    private String country;

    private Long labelTagId;

    @TableField(exist = false)
    private NewActivityConfig activityConfig;

    public Integer getStatus() {
        return null != activityConfig?1:0;
    }

    private Integer type;

    /**
     * 活动规则描述
     */
    @ApiModelProperty("活动规则描述")
    private String ruleDes;

    private boolean isShop;

    /**
     * 几天内
     */
    @ApiModelProperty("几天内")
    private Long goodsRunDays;

    /**
     * 最低单价
     */
    @ApiModelProperty("最低单价")
    private Integer sales;

    /**
     * 0 - 不限制商家 1 - 限制商家
     */
    @ApiModelProperty("0 - 不限制商家 1 - 限制商家")
    private Integer shopLimit;

    @ApiModelProperty("商家id 用回车分割")
    private String shopIdList;

    private Long shopId;

    private Integer shopNum;

    private String shopIdString;

    //是否是终审 0非终审 1终审
    private Integer isFinalReview = 0;
    /**
     * 关键字 y用于匹配id 或者名称
     */
    private String keyword;
}
