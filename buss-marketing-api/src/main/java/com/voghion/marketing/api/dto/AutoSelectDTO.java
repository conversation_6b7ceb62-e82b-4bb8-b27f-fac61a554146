package com.voghion.marketing.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class AutoSelectDTO {

  private Long id;

  /**
   * 商品名称相同是否只入选最低价 0 - 否 1 - 是
   */
  @ApiModelProperty(value = "商品名称相同是否只入选最低价 0 - 否 1 - 是")
  private Integer isSameName;

    /**
     * 类目最高价格设置
     */
    @ApiModelProperty(value = "类目最高价格设置")
    private List<AutoSelectCategoryDTO> autoSelectCategoryDTOList;

    /**
     * 淘汰类型 1 - flashdeal 2 - 运营选品  3 - 虚拟商品列表
     */
    @ApiModelProperty(value = "淘汰类型 1 - flashdeal 2 - 运营选品  3 - 虚拟商品列表 4 - 满减活动")
    private Integer type;

    /**
     * 类型关联id
     */
    @ApiModelProperty(value = "类型关联id")
    private Long typeId;



    /**
     * 入选动销条件
     */
    @ApiModelProperty(value = "入选动销条件")
   private AutoSelectSalesDTO autoSelectSalesDTO;


    /**
     * 是否负向商品 1 - 是 0 - 否
     */
    private Integer isNegative;

    List<GoodsSummaryDTO> goodsSummaryDTOList;

    /**
     * 商品进入活动天数下限
     */
    @ApiModelProperty(value = "商品进入活动天数下限")
    private Integer createDaysLower;

    /**
     * 淘汰类型 0 - 沉底  1 - 剔除
     */
    private Integer eliminationType;

    /**
     * 均价限制  0 - 不应用 1 - 应用
     */
    @ApiModelProperty(value = "均价限制  0 - 不应用 1 - 应用")
    private Integer averagePriceCheck;

    @ApiModelProperty("商家id 用回车分割")
    private String shopIdList;

    @ApiModelProperty("商家标签ids白名单 ")
    private List<TagDTO> shopTagIdList;

    @ApiModelProperty(value = "商品活动价小于N天单价 0 7 15 30 60 90")
    private Integer goodsActivityPriceDays;

    @ApiModelProperty(value = "商品创建时间 近7 15 30 60 90 180 360 天")
    private Integer goodsCreateDays;

    @ApiModelProperty(value = "商品标签")
    private String goodsTagIdList;

    @ApiModelProperty(value = "商品活动价是否低于活动建议价格 0 否 1 是")
    private Integer isActivityPriceLow;

    @ApiModelProperty(value = "售卖区域")
    private String saleAreaList;

    private String createBy;
    private String updateBy;
    private Date createTime;
    private Date updateTime;
    @ApiModelProperty(value = "模板名")
    private String name;
}
