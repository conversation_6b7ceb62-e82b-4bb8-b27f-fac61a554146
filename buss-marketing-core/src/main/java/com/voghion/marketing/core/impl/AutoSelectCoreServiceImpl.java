package com.voghion.marketing.core.impl;

import com.alibaba.fastjson.JSON;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.TransferUtils;
import com.voghion.es.impl.BaseEsQueryServiceImpl;
import com.voghion.marketing.api.dto.*;
import com.voghion.marketing.client.GoodsTagClientFactory;
import com.voghion.marketing.core.AutoSelectCoreService;
import com.voghion.marketing.model.dto.GoodsEveryDayModel;
import com.voghion.marketing.model.enums.AutoSelectStatus;
import com.voghion.marketing.model.enums.MarketingResultCode;
import com.voghion.marketing.model.po.*;
import com.voghion.marketing.service.*;
import com.voghion.marketing.util.BeanCopyUtil;
import com.voghion.product.api.dto.GoodsTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AutoSelectCoreServiceImpl implements AutoSelectCoreService {
    @Resource
    private AutoSelectConfigService autoSelectConfigService;
    @Resource
    private AutoSelectCategoryConfigService autoSelectCategoryConfigService;
    @Resource
    private AutoEliminationConfigService autoEliminationConfigService;
    @Resource
    private AutoSelectCategoryDefaultConfigService autoSelectCategoryDefaultConfigService;

    @Resource
    private NewActivityService newActivityService;
    @Resource
    private ActivityFullReductionService activityFullReductionService;
    @Resource
    private CustomListItemsService customListItemsService;
    @Resource
    private BaseEsQueryServiceImpl baseEsQueryServiceImpl;
    @Resource
    private BusinessLogService businessLogService;
    private int taskNum = 1;
    @Resource
    private GoodsTagClientFactory goodsTagClientFactory;

    @Transactional
    @Override
    public void setAutoSelectConfig(AutoSelectDTO autoSelectDTO) {
        log.info("setAutoSelectConfig start, autoSelectDTO = {}", JSON.toJSONString(autoSelectDTO));
        CheckUtils.check(Objects.isNull(autoSelectDTO) || Objects.isNull(autoSelectDTO.getType()) || Objects.isNull(autoSelectDTO.getTypeId()), MarketingResultCode.PARAMETER_ERROR);

        // 删除已有配置
        deleteConfig(autoSelectDTO);

        Set<Long> shopIdList = null;
        if(StringUtils.isNotBlank(autoSelectDTO.getShopIdList())){
           shopIdList = Arrays.stream(autoSelectDTO.getShopIdList().trim().split("\\n"))
                    .filter(s -> !s.equals(" ") && StringUtils.isNotBlank(s))
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toSet());
        }

        String shopTagIdList = null;
        if (CollectionUtils.isNotEmpty(autoSelectDTO.getShopTagIdList())) {
            shopTagIdList = autoSelectDTO.getShopTagIdList().stream().map(TagDTO::getId).map(String::valueOf).collect(Collectors.joining(","));
        }

        // 入选规则配置
        AutoSelectConfig autoSelectConfig = new AutoSelectConfig();
        BeanCopyUtil.copyProperties(autoSelectDTO, autoSelectConfig);
//        if (Objects.nonNull(autoSelectDTO.getIsSameName())
//                || CollectionUtils.isNotEmpty(autoSelectDTO.getAutoSelectCategoryDTOList())
//                || Objects.nonNull(autoSelectDTO.getAutoSelectSalesDTO())
//                || Objects.nonNull(autoSelectDTO.getIsNegative())
//                || Objects.nonNull(autoSelectDTO.getAveragePriceCheck())
//                || StringUtils.isNotBlank(autoSelectDTO.getShopIdList())
//                || CollectionUtils.isNotEmpty(autoSelectDTO.getShopTagIdList())
//        ) {
        AutoSelectSalesDTO autoSelectSalesDTO = autoSelectDTO.getAutoSelectSalesDTO();
        if (Objects.nonNull(autoSelectSalesDTO)) {
            autoSelectConfig.setRunDays(autoSelectSalesDTO.getRunDays());
            autoSelectConfig.setSales(autoSelectSalesDTO.getSales());
            autoSelectConfig.setOverGoodsNum(autoSelectSalesDTO.getOverGoodsNum());
            autoSelectConfig.setOverLimitNum(autoSelectSalesDTO.getOverLimitNum());
            autoSelectConfig.setSameTop(autoSelectSalesDTO.getSameTop());
        }
        autoSelectConfig.setWhiteShopList(shopIdList != null ? StringUtils.join(shopIdList, ",") : "");
        autoSelectConfig.setWhiteShopTagList(shopTagIdList != null ? shopTagIdList : "");
        autoSelectConfigService.insert(autoSelectConfig);
//        }
        List<AutoSelectCategoryDTO> autoSelectCategoryDTOList = autoSelectDTO.getAutoSelectCategoryDTOList();

        // 入选类目最高价规则
        if (CollectionUtils.isNotEmpty(autoSelectCategoryDTOList)) {
            List<AutoSelectCategoryConfig> collect = autoSelectCategoryDTOList.stream()
                    .map(d -> {
                        AutoSelectCategoryConfig autoSelectCategoryConfig = new AutoSelectCategoryConfig();
                        autoSelectCategoryConfig.setCategoryId(d.getCategoryId());
                        autoSelectCategoryConfig.setCategoryName(d.getCategoryName());
                        autoSelectCategoryConfig.setMaxAmount(d.getMaxAmount());
                        autoSelectCategoryConfig.setSelectId(autoSelectConfig.getId());
                        autoSelectCategoryConfig.setLevel(d.getLevel());
                        return autoSelectCategoryConfig;
                    }).collect(Collectors.toList());
            autoSelectCategoryConfigService.insertBatch(collect);
        }

        // 淘汰规则
        List<GoodsSummaryDTO> goodsSummaryDTOList = autoSelectDTO.getGoodsSummaryDTOList();
        if (CollectionUtils.isNotEmpty(goodsSummaryDTOList)
                || Objects.nonNull(autoSelectDTO.getCreateDaysLower())
        ) {
            if(CollectionUtils.isNotEmpty(goodsSummaryDTOList)){
                List<AutoEliminationConfig> collect = goodsSummaryDTOList.stream()
                        .map(goodsSummaryDTO -> {
                            AutoEliminationConfig autoEliminationConfig = new AutoEliminationConfig();
                            BeanCopyUtil.copyProperties(goodsSummaryDTO, autoEliminationConfig);
                            autoEliminationConfig.setCreateDaysLower(autoSelectDTO.getCreateDaysLower());
                            autoEliminationConfig.setEliminationType(autoSelectDTO.getEliminationType());
                            autoEliminationConfig.setType(autoSelectDTO.getType());
                            autoEliminationConfig.setTypeId(autoSelectDTO.getTypeId());
                            return autoEliminationConfig;
                        }).collect(Collectors.toList());
                autoEliminationConfigService.insertBatch(collect);
            }else {
                AutoEliminationConfig autoEliminationConfig = new AutoEliminationConfig();
                BeanCopyUtil.copyProperties(autoSelectDTO, autoEliminationConfig);
                autoEliminationConfigService.insert(autoEliminationConfig);
            }
        }

        log.info("setAutoSelectConfig end, autoSelectDTO = {}", JSON.toJSONString(autoSelectDTO));
    }

    private void deleteConfig(AutoSelectDTO autoSelectDTO) {
        log.info("setAutoSelectConfig deleteConfig autoSelectDTO = {}", JSON.toJSONString(autoSelectDTO));
        List<AutoSelectConfig> autoSelectConfigs = autoSelectConfigService.lambdaQuery().eq(AutoSelectConfig::getType, autoSelectDTO.getType()).eq(AutoSelectConfig::getTypeId, autoSelectDTO.getTypeId()).list();
        if (CollectionUtils.isEmpty(autoSelectConfigs)) {
            return;
        }

        boolean autoSelectConfigDelete = autoSelectConfigService.deleteByIds(autoSelectConfigs.stream().map(AutoSelectConfig::getId).collect(Collectors.toList()));
        log.info("删除配置结果 {}", autoSelectConfigDelete);

        List<AutoEliminationConfig> eliminationConfig = autoEliminationConfigService.lambdaQuery().eq(AutoEliminationConfig::getType, autoSelectDTO.getType()).eq(AutoEliminationConfig::getTypeId, autoSelectDTO.getTypeId()).list();
        if (CollectionUtils.isNotEmpty(eliminationConfig)) {
            List<Long> autoEliminationConfigIds = eliminationConfig.stream().map(AutoEliminationConfig::getId).collect(Collectors.toList());
            autoEliminationConfigService.deleteByIds(autoEliminationConfigIds);
            log.info("setAutoSelectConfig deleteConfig  eliminationConfigIds = {}", JSON.toJSONString(autoEliminationConfigIds));
        }

        autoSelectConfigs.forEach(autoSelectConfig -> {
            Boolean autoSelectCategoryConfigDelete = autoSelectCategoryConfigService.delete(new AutoSelectCategoryConfig().setSelectId(autoSelectConfig.getId()));
            log.info("删除类目结果 {}", autoSelectCategoryConfigDelete);
        });
    }

    @Override
    public void updateDefaultAutoSelectConfig(AutoSelectDefaultCategoryDTO autoSelectDefaultCategoryDTO) {
        CheckUtils.check(Objects.isNull(autoSelectDefaultCategoryDTO)
                || CollectionUtils.isEmpty(autoSelectDefaultCategoryDTO.getAaAutoSelectCategoryDTOList())
                || Objects.isNull(autoSelectDefaultCategoryDTO.getType()), MarketingResultCode.PARAMETER_ERROR);
        // 先删除
        autoSelectCategoryDefaultConfigService.delete(new AutoSelectCategoryDefaultConfig().setType(autoSelectDefaultCategoryDTO.getType()));
        List<AutoSelectCategoryDefaultConfig> collect = autoSelectDefaultCategoryDTO.getAaAutoSelectCategoryDTOList().stream()
                .map(dto -> {
                    AutoSelectCategoryDefaultConfig autoSelectCategoryDefaultConfig = new AutoSelectCategoryDefaultConfig();
                    BeanCopyUtil.copyProperties(dto, autoSelectCategoryDefaultConfig);
                    autoSelectCategoryDefaultConfig.setType(autoSelectDefaultCategoryDTO.getType());
                    return autoSelectCategoryDefaultConfig;
                }).collect(Collectors.toList());
        autoSelectCategoryDefaultConfigService.insertBatch(collect);
    }

    @Override
    public List<AutoSelectCategoryDTO> getDefaultAutoSelectConfig(Integer type) {
        return TransferUtils.transferList(autoSelectCategoryDefaultConfigService.lambdaQuery().eq(AutoSelectCategoryDefaultConfig::getType, type).list(),
                AutoSelectCategoryDTO.class);
    }

    @Override
    public AutoSelectDTO getAutoSelectCategoryDTO(AutoSelectQueryDTO queryDTO) {
        AutoSelectDTO selectDTO = new AutoSelectDTO();

        // 淘汰规则
        List<AutoEliminationConfig> autoEliminationConfigList = autoEliminationConfigService.lambdaQuery()
                .eq(AutoEliminationConfig::getType, queryDTO.getType())
                .eq(AutoEliminationConfig::getTypeId, queryDTO.getTypeId()).list();
        if (CollectionUtils.isNotEmpty(autoEliminationConfigList)){
            List<GoodsSummaryDTO> collect = autoEliminationConfigList.stream()
                    .map(autoEliminationConfig -> {
                        GoodsSummaryDTO goodsSummaryDTO = new GoodsSummaryDTO();
                        BeanCopyUtil.copyProperties(autoEliminationConfig, goodsSummaryDTO);
                        return goodsSummaryDTO;
                    }).collect(Collectors.toList());
            BeanCopyUtil.copyProperties(autoEliminationConfigList.get(0), selectDTO);
            selectDTO.setGoodsSummaryDTOList(collect);
        }

        // 入选规则
        AutoSelectConfig autoSelectConfig = autoSelectConfigService.lambdaQuery()
                .eq(AutoSelectConfig::getType, queryDTO.getType()).eq(AutoSelectConfig::getTypeId, queryDTO.getTypeId()).one();
        if (Objects.nonNull(autoSelectConfig)) {
            selectDTO.setIsSameName(autoSelectConfig.getIsSameName());
            selectDTO.setIsNegative(autoSelectConfig.getIsNegative());
            selectDTO.setAveragePriceCheck(autoSelectConfig.getAveragePriceCheck());
            List<AutoSelectCategoryConfig> list = autoSelectCategoryConfigService.lambdaQuery()
                    .eq(AutoSelectCategoryConfig::getSelectId, autoSelectConfig.getId()).list();
            selectDTO.setAutoSelectCategoryDTOList(list.stream()
                    .map(e -> BeanCopyUtil.transform(e, AutoSelectCategoryDTO.class)).collect(Collectors.toList()));
            selectDTO.setGoodsActivityPriceDays(autoSelectConfig.getGoodsActivityPriceDays());
            selectDTO.setGoodsCreateDays(autoSelectConfig.getGoodsCreateDays());
            selectDTO.setGoodsTagIdList(autoSelectConfig.getGoodsTagIdList());
            selectDTO.setIsActivityPriceLow(autoSelectConfig.getIsActivityPriceLow());
            selectDTO.setSaleAreaList(autoSelectConfig.getSaleAreaList());

            AutoSelectSalesDTO autoSelectSalesDTO = new AutoSelectSalesDTO();
            autoSelectSalesDTO.setSales(autoSelectConfig.getSales());
            autoSelectSalesDTO.setRunDays(autoSelectConfig.getRunDays());
            autoSelectSalesDTO.setOverGoodsNum(autoSelectConfig.getOverGoodsNum());
            autoSelectSalesDTO.setOverLimitNum(autoSelectConfig.getOverLimitNum());
            autoSelectSalesDTO.setSameTop(autoSelectConfig.getSameTop());
            if(StringUtils.isNotBlank(autoSelectConfig.getWhiteShopList())){
                autoSelectSalesDTO.setShopIdList(autoSelectConfig.getWhiteShopList().replace(",","\n"));
            }

            if (StringUtils.isNotBlank(autoSelectConfig.getWhiteShopTagList())){
                List<Long> ids = Arrays.stream(autoSelectConfig.getWhiteShopTagList().split(",")).map(e->{
                    try{
                        return Long.valueOf(e);
                    }catch (Exception ignored){}
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(ids)){
                    List<GoodsTagDTO> goodsTagDTOS =  goodsTagClientFactory.queryGoodsTagByTagIds(ids);
                    autoSelectSalesDTO.setShopTagIdList(goodsTagDTOS.stream().map(e -> {
                        TagDTO tagDTO = new TagDTO();
                        tagDTO.setName(e.getTagName());
                        tagDTO.setId(e.getId());
                        return tagDTO;
                    }).collect(Collectors.toList()));
                }
            }

            selectDTO.setAutoSelectSalesDTO(autoSelectSalesDTO);

        }
        return selectDTO;
    }

    @Override
    public void autoElimination() {
        List<AutoEliminationConfig> autoEliminationConfigList = autoEliminationConfigService.lambdaQuery().list();
        if (CollectionUtils.isEmpty(autoEliminationConfigList)) {
            log.info("autoElimination autoEliminationConfigList is empty.");
            return;
        }
        List<Long> newActivityIdList = autoEliminationConfigList.stream()
                .filter(e -> Objects.equals(e.getType(), 1))
                .map(AutoEliminationConfig::getTypeId).collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        Map<Long, NewActivity> newActivityMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(newActivityIdList)){
            List<NewActivity> effectiveActivityList = newActivityService.lambdaQuery()
                    .in(NewActivity::getId, newActivityIdList)
                    .eq(NewActivity::getIsDel, 0)
                    .lt(NewActivity::getEffectStartTime, now.plusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .gt(NewActivity::getEffectEndTime, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).list();
            log.info("autoElimination effectiveActivityList = {}", JSON.toJSONString(effectiveActivityList));
            newActivityMap = effectiveActivityList.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));
        }
        Map<Long, NewActivity> finalNewActivityMap = newActivityMap;

        List<Long> fullNewActivityIdList = autoEliminationConfigList.stream()
                .filter(e -> Objects.equals(e.getType(), 4))
                .map(AutoEliminationConfig::getTypeId).collect(Collectors.toList());
        Map<Long, ActivityFullReduction> fullNewActivityMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(fullNewActivityIdList)){
            List<ActivityFullReduction> effectiveActivityList = activityFullReductionService.lambdaQuery()
                    .in(ActivityFullReduction::getId, fullNewActivityIdList)
                    .eq(ActivityFullReduction::getIsDel, 0)
                    .lt(ActivityFullReduction::getActivityStartTime, now.plusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .gt(ActivityFullReduction::getActivityEndTime, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).list();
            log.info("autoElimination effectiveActivityList = {}", JSON.toJSONString(effectiveActivityList));
            fullNewActivityMap = effectiveActivityList.stream().collect(Collectors.toMap(ActivityFullReduction::getId, Function.identity()));
        }
        Map<Long, ActivityFullReduction> fullFinalNewActivityMap = fullNewActivityMap;

        autoEliminationConfigList.forEach(autoEliminationConfig -> {
            if (Objects.equals(autoEliminationConfig.getType(), 1)) {
                NewActivity newActivity = finalNewActivityMap.get(autoEliminationConfig.getTypeId());
                if (Objects.isNull(newActivity)) {
                    log.info("autoElimination newActivity is not effect, id = {}.", autoEliminationConfig.getTypeId());
                    return;
                }
                Long virGoodsItemsId = newActivity.getVirGoodsItemsId();
                if (Objects.isNull(virGoodsItemsId)) {
                    log.info("autoElimination virGoodsItemsId is null, id = {}.", autoEliminationConfig.getTypeId());
                    return;
                }
                reduceSort(virGoodsItemsId, autoEliminationConfig);
            } else if (Objects.equals(autoEliminationConfig.getType(), 3)) {
                reduceSort(autoEliminationConfig.getTypeId(), autoEliminationConfig);
            } else if(Objects.equals(autoEliminationConfig.getType(), 4)){
                ActivityFullReduction fullNewActivity = fullFinalNewActivityMap.get(autoEliminationConfig.getTypeId());
                if (Objects.isNull(fullNewActivity)) {
                    log.info("autoElimination newActivity is not effect, id = {}.", autoEliminationConfig.getTypeId());
                    return;
                }
                Long virGoodsItemsId = fullNewActivity.getVirGoodsItemsId();
                if (Objects.isNull(virGoodsItemsId)) {
                    log.info("autoElimination virGoodsItemsId is null, id = {}.", autoEliminationConfig.getTypeId());
                    return;
                }
                reduceSort(virGoodsItemsId, autoEliminationConfig);

            }
            else {
                log.info("autoElimination type not support, autoEliminationConfig = {}", autoEliminationConfig);
            }
        });
        taskNum++;
    }

    private void reduceSort(Long customId, AutoEliminationConfig autoEliminationConfig) {
        long id = 1L;
        List<CustomListItems> list;
        long start = System.currentTimeMillis();
        do {
            list = customListItemsService.lambdaQuery()
                    .ge(CustomListItems::getId, id)
                    .eq(CustomListItems::getCustomId, customId)
                    .lt(CustomListItems::getAutoSelectStatus, AutoSelectStatus.OUT.getCode())
                    .orderByAsc(CustomListItems::getId)
                    .last("limit 100")
                    .list();
            log.info("reduceSort customId ={}, size = {}", customId, list.size());
            if (CollectionUtils.isEmpty(list)) {
                log.info("reduceSort finish, customId ={}, type id = {}, id = {}, cost = {} ms ", customId, autoEliminationConfig.getId(), id, (System.currentTimeMillis() - start));
                break;
            }
            id = list.get(list.size() - 1).getId() + 1;
            reduceListSort(customId, list, autoEliminationConfig);
        } while (CollectionUtils.isNotEmpty(list));
    }
    private void reduceListSort(Long customId, List<CustomListItems> list, AutoEliminationConfig autoEliminationConfig){
        List<Long> goodsIdList = list.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
        Map<Long, CustomListItems> needOutMap = new HashMap<>();

        StringBuilder stringBuilder = new StringBuilder();

        // 曝光量和订单成交量判断
        Integer recentDays = autoEliminationConfig.getRecentDays();
        Integer pvLower = autoEliminationConfig.getPvLower();
        Integer ordersUpper = autoEliminationConfig.getOrdersUpper();
        Integer eliminationType = autoEliminationConfig.getEliminationType();
        BigDecimal addCartRateUpper = autoEliminationConfig.getAddCartRateUpper();
        BigDecimal clickUvRateUpper = autoEliminationConfig.getClickUvRateUpper();
        if(Objects.nonNull(recentDays) && Objects.nonNull(eliminationType)){
            log.info("reduceListSort customId ={}, enter pv lower", customId);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("runDays", recentDays));
            boolQueryBuilder.must(QueryBuilders.termsQuery("goodsId", goodsIdList));
            //曝光量
            if(Objects.nonNull(pvLower)){
                boolQueryBuilder.must(QueryBuilders.rangeQuery("showUv").gte(pvLower));
            }
//            //曝光量
//            if(Objects.nonNull(autoEliminationConfig.getMinShowUv())){
//                boolQueryBuilder.must(QueryBuilders.rangeQuery("showUv").gte(autoEliminationConfig.getMinShowUv()));
//            }
            if(Objects.nonNull(autoEliminationConfig.getMaxShowUv())){
                boolQueryBuilder.must(QueryBuilders.rangeQuery("showUv").lte(autoEliminationConfig.getMaxShowUv()));
            }
            //点击 clickUv
            if (Objects.nonNull(autoEliminationConfig.getMaxClickUv())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("clickUv").lte(autoEliminationConfig.getMaxClickUv()));
            }
            //加购UV
            if (Objects.nonNull(autoEliminationConfig.getMaxAddUv())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("addUv").lte(autoEliminationConfig.getMaxAddUv()));
            }
            //成交单数
            if (Objects.nonNull(ordersUpper)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("dealCnt").lte(ordersUpper));
            }
//            if (Objects.nonNull(autoEliminationConfig.getMaxDealCnt())){
//                boolQueryBuilder.must(QueryBuilders.rangeQuery("dealCnt").lte(autoEliminationConfig.getMaxDealCnt()));
//            }
            //成交uv
            if (Objects.nonNull(autoEliminationConfig.getMaxDealUv())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("dealUv").lte(autoEliminationConfig.getMaxDealUv()));
            }
            //gmv
            if (Objects.nonNull(autoEliminationConfig.getMaxDealPayMoney())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("dealPayMoney").lte(autoEliminationConfig.getMaxDealPayMoney()));
            }
            //uv点击率
            if (Objects.nonNull(clickUvRateUpper)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("clickRateUv").lte(clickUvRateUpper));
            }
            //uv加购率 addRateUv
            if (Objects.nonNull(addCartRateUpper)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("addRateUv").lte(addCartRateUpper));
            }
//            //uv加购率 addRateUv
//            if (Objects.nonNull(autoEliminationConfig.getMaxAddRateUv())) {
//                boolQueryBuilder.must(QueryBuilders.rangeQuery("addRateUv").lte(autoEliminationConfig.getMaxAddRateUv()));
//            }
            //uv支付率
            if (Objects.nonNull(autoEliminationConfig.getMaxDealRateUv())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("dealRateUv").lte(autoEliminationConfig.getMaxDealRateUv()));
            }
            //ecmp
            if (Objects.nonNull(autoEliminationConfig.getMaxEcpm())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("ecpm").lte(autoEliminationConfig.getMaxEcpm()));
            }

            List<GoodsEveryDayModel> goodsEveryDayModels = baseEsQueryServiceImpl.searchData(new SearchRequest("goods_every_day_es_a")
                            .source(new SearchSourceBuilder()
                                    .query(boolQueryBuilder
                                    )
                            )
                    , GoodsEveryDayModel.class);
            log.info("reduceListSort customId ={}, goodsEveryDayModels = {}", customId, JSON.toJSONString(goodsEveryDayModels));
            Map<Long, GoodsEveryDayModel> dayModelMap = goodsEveryDayModels.stream().collect(Collectors.toMap(GoodsEveryDayModel::getGoodsId, Function.identity()));

            needOutMap = list.stream()
                    .filter(e -> Objects.nonNull(dayModelMap.get(e.getGoodsId())))
                    .peek(e -> {
                        e.setShowArea(-1);
                        e.setAutoSelectStatus(AutoSelectStatus.OUT.getCode());
                        String failReason = getAutoSelectFailReason(autoEliminationConfig, dayModelMap.get(e.getGoodsId()));
                        e.setAutoSelectFailReason(failReason);
//                        e.setAutoSelectFailReason("成交量不足" + autoEliminationConfig.getMaxDealCnt() + "单");
                        e.setAutoSelectTime(LocalDateTime.now());
                    })
                    .collect(Collectors.toMap(CustomListItems::getGoodsId, Function.identity(), (a, b) -> a));
            if(!needOutMap.isEmpty()){
                stringBuilder.append("not enough deal cnt goods id : ")
                        .append(needOutMap.values().stream().map(CustomListItems::getGoodsId).collect(Collectors.toList()));
            }
        }

//        // 创建时间判断
//        if (Objects.nonNull(autoEliminationConfig.getCreateDaysLower()) && autoEliminationConfig.getCreateDaysLower() > 0) {
//            log.info("reduceSort customId ={}, enter create days", customId);
//            stringBuilder.append("\nnot enough create time goods id : ");
//            for (CustomListItems customListItems : list) {
//                if (customListItems.getCreateTime().plusDays(autoEliminationConfig.getCreateDaysLower()).isBefore(LocalDateTime.now())) {
//                    customListItems.setShowArea(-1);
//                    customListItems.setAutoSelectStatus(AutoSelectStatus.OUT.getCode());
//                    customListItems.setAutoSelectFailReason("进入活动时间大于" + autoEliminationConfig.getCreateDaysLower() + "天");
//                    customListItems.setAutoSelectTime(LocalDateTime.now());
//                    needOutMap.put(customListItems.getGoodsId(), customListItems);
//                    stringBuilder.append(customListItems.getGoodsId());
//                }
//            }
//        }

        log.info("reduceSort customId = {}, autoEliminationConfig id = {}, needUpdateMap ={}", customId, autoEliminationConfig.getId(), needOutMap);
        if (!needOutMap.isEmpty()) {
            Collection<CustomListItems> values = needOutMap.values();
            //0沉底 1剔除
            if(Objects.equals(eliminationType, 0)){
                customListItemsService.updateBatchById(values);
            }else{
                customListItemsService.deleteByIds(values.stream().map(CustomListItems::getId).collect(Collectors.toList()));
            }

            // 记录日志
            BusinessLog businessLog = new BusinessLog();
            businessLog.setBusinessDesc("定时任务淘汰虚拟商品列表");
            businessLog.setChangeDesc(stringBuilder.toString());
            businessLog.setKey1("reduceSort_" + customId.toString() + "_" + eliminationType);
            businessLog.setKey2(LocalDate.now() + String.valueOf(taskNum));
            businessLogService.insert(businessLog);
        }
    }

    /**
     * 设置值淘汰原因
     * @param autoEliminationConfig
     * @param goodsEveryDayModel
     * @return
     */
    public String getAutoSelectFailReason(AutoEliminationConfig autoEliminationConfig,GoodsEveryDayModel goodsEveryDayModel){
        if (autoEliminationConfig == null || goodsEveryDayModel == null) {
            return "数据异常";
        }

        List<String> failReasons = new ArrayList<>();
        //老字段兼容
        if (checkLong(autoEliminationConfig.getPvLower(), goodsEveryDayModel.getShowPv())) {
            failReasons.add("曝光量超过" + autoEliminationConfig.getPvLower());
        }
//        if (checkLong(goodsEveryDayModel.getShowUv(), autoEliminationConfig.getMinShowUv())) {
//            failReasons.add("曝光量超过" + autoEliminationConfig.getMinShowUv());
//        }
        // 曝光量判断
        if (checkLong(autoEliminationConfig.getMaxShowUv(), goodsEveryDayModel.getShowUv())) {
            failReasons.add("曝光量不足" + autoEliminationConfig.getMaxShowUv());
        }

        if (checkLong(autoEliminationConfig.getOrdersUpper(), goodsEveryDayModel.getDealCnt())) {
            failReasons.add("成交单数不足" + autoEliminationConfig.getOrdersUpper());
        }
//        // 成交单数判断
//        if (checkLong(autoEliminationConfig.getMaxDealCnt(), goodsEveryDayModel.getDealCnt())) {
//            failReasons.add("成交量不足" + autoEliminationConfig.getMaxDealCnt() + "单");
//        }

        if (checkBigDecimal(autoEliminationConfig.getClickUvRateUpper(), goodsEveryDayModel.getClickRateUv())) {
            failReasons.add("uv点击率不足" + autoEliminationConfig.getClickUvRateUpper());
        }
//        // UV点击率判断
//        if (checkBigDecimal(autoEliminationConfig.getMaxClickRateUv(), goodsEveryDayModel.getClickRateUv())) {
//            failReasons.add("uv点击率不足" + autoEliminationConfig.getMaxClickRateUv());
//        }

        // UV加购率判断
        if (checkBigDecimal(autoEliminationConfig.getAddCartRateUpper(), goodsEveryDayModel.getAddRateUv())) {
            failReasons.add("uv加购率不足" + autoEliminationConfig.getAddCartRateUpper());
        }
//        // UV加购率判断
//        if (checkBigDecimal(autoEliminationConfig.getMaxAddRateUv(), goodsEveryDayModel.getAddRateUv())) {
//            failReasons.add("uv加购率不足" + autoEliminationConfig.getMaxAddRateUv());
//        }

        // 点击UV判断
        if (checkLong(autoEliminationConfig.getMaxClickUv(), goodsEveryDayModel.getClickUv())) {
            failReasons.add("点击UV不足" + autoEliminationConfig.getMaxClickUv());
        }

        // 加购UV判断
        if (checkLong(autoEliminationConfig.getMaxAddUv(), goodsEveryDayModel.getAddUv())) {
            failReasons.add("加购UV不足" + autoEliminationConfig.getMaxAddUv());
        }

        // 成交UV判断
        if (checkLong(autoEliminationConfig.getMaxDealUv(), goodsEveryDayModel.getDealUv())) {
            failReasons.add("成交UV不足" + autoEliminationConfig.getMaxDealUv());
        }

        // ECPM判断
        if (checkLong(autoEliminationConfig.getMaxEcpm(), goodsEveryDayModel.getEcpm())) {
            failReasons.add("ecpm不足" + autoEliminationConfig.getMaxEcpm());
        }

        // UV支付率判断
        if (checkBigDecimal(autoEliminationConfig.getMaxDealRateUv(), goodsEveryDayModel.getDealRateUv())) {
            failReasons.add("uv支付率不足" + autoEliminationConfig.getMaxDealRateUv());
        }

        // GMV判断
        if (checkBigDecimal(autoEliminationConfig.getMaxDealPayMoney(), goodsEveryDayModel.getDealPayMoney())) {
            failReasons.add("GMV不足" + autoEliminationConfig.getMaxDealPayMoney());
        }

        return String.join("，", failReasons);

    }

    /**
     * 判断是否需要添加失败原因（针对Long类型）
     * @param configValue 配置阈值
     * @param actualValue 实际值
     * @return 是否需要添加失败原因
     */
    private boolean checkLong(Integer configValue, Integer actualValue) {
        return Objects.nonNull(configValue)
                && Objects.nonNull(actualValue)
                && actualValue > configValue;
    }

    /**
     * 判断是否需要添加失败原因
     * @param configValue 配置阈值
     * @param actualValue 实际值
     * @return 是否需要添加失败原因
     */
    private boolean checkBigDecimal(BigDecimal configValue, BigDecimal actualValue) {
        return Objects.nonNull(configValue)
                && Objects.nonNull(actualValue)
                && actualValue.compareTo(configValue) > 0;
    }

    public void deleteDuplicateConfig(Long typeId) {
        List<AutoSelectConfig> list = autoSelectConfigService.lambdaQuery().eq(AutoSelectConfig::getTypeId, typeId).list();
        autoSelectConfigService.removeByIds(list.stream().map(AutoSelectConfig::getId).collect(Collectors.toList()));
    }
}
