package com.voghion.marketing.biz.activity;

import com.voghion.es.vo.GoodsESVo;
import com.voghion.marketing.dto.ActivityFirstSelectDTO;
import com.voghion.marketing.enums.ActivityFirstSelectFail;
import com.voghion.marketing.model.po.AutoSelectConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 活动售卖区域校验
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SaleAreaCheck implements ActivityGoodsSelectCheck {

  @Override
  public ActivityFirstSelectDTO check(ActivityGoodsSelectCheckParam checkParam) {
    AutoSelectConfig autoSelectConfig = checkParam.getAutoSelectConfig();
    ActivityGoods activityGoods = checkParam.getActivityGoods();

    if (StringUtils.isEmpty(autoSelectConfig.getSaleAreaList())) {
      log.info("setAutoSelect goods id = {}, sale area is empty", activityGoods.getGoodsId());
      return null;
    }

    List<String> areas = Arrays.asList(autoSelectConfig.getSaleAreaList().trim().split(","));

    GoodsESVo goodsEsVo = checkParam.getGoodsEsVoMap().get(activityGoods.getGoodsId());

    log.info("setAutoSelect goods id = {}, sale area = {}", activityGoods.getGoodsId(), goodsEsVo.getCountry());
    String country = goodsEsVo.getCountry();
    if (StringUtils.isBlank(country)) {
      return buildFailDTO();
    }

    List<String> goodsCountry = Arrays.asList(country.trim().split(","));
    if (goodsCountry.isEmpty() || Collections.disjoint(areas, goodsCountry)) {
      return buildFailDTO();
    }

    return null;
  }

  private ActivityFirstSelectDTO buildFailDTO() {
    ActivityFirstSelectDTO dto = new ActivityFirstSelectDTO();
    dto.setFirstSelectFail(ActivityFirstSelectFail.SALE_AREA);
    return dto;
  }
}
