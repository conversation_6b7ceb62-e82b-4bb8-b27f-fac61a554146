CREATE TABLE `new_activity` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动id',
                                `name` varchar(255) NOT NULL COMMENT '活动名称',
                                `cat_type` tinyint(4) DEFAULT '0' COMMENT '0-不限制 1-限定类目 2-排除类目',
                                `cate_col` varchar(255) DEFAULT NULL COMMENT '后台类目集合（,分开）',
                                `sign_start_time` datetime DEFAULT NULL COMMENT '开始报名时间',
                                `sign_end_time` datetime DEFAULT NULL COMMENT '报名结束时间',
                                `effect_start_time` datetime NOT NULL COMMENT '开始生效时间',
                                `effect_end_time` datetime NOT NULL COMMENT '结束时间',
                                `is_del` int(2) DEFAULT '0' COMMENT '是否删除  1 删除  0 未删除',
                                `lowest_discount` decimal(4,2) DEFAULT NULL COMMENT '最低折扣',
                                `show_discount_min` decimal(4,2) DEFAULT NULL COMMENT 'App展示最低折扣',
                                `show_discount_max` decimal(4,2) DEFAULT NULL COMMENT 'App展示最高折扣',
                                `sign_up_total` bigint(8) DEFAULT NULL COMMENT '可报名数量',
                                `state_type` tinyint(4) DEFAULT '0' COMMENT '状态类型 0未改价 1已改价 2已恢复价格',
                                `activity_status` tinyint(4) NOT NULL DEFAULT '10' COMMENT ' 活动状态（10-待开始 20-进行中 30-已暂停 40-已过期 50-已删除)',
                                `type` tinyint(4) DEFAULT NULL COMMENT '1 商家报名   2七日达',
                                `country` varchar(255) DEFAULT NULL COMMENT '活动国家',
                                `create_time` datetime DEFAULT NULL,
                                `update_time` datetime DEFAULT NULL,
                                `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
                                `vir_goods_items_id` bigint(17) DEFAULT NULL COMMENT '同步的虚拟商品列表id',
                                `label_tag_id` bigint(17) DEFAULT NULL COMMENT '标签图标签id',
                                `rule_des` text COMMENT '活动规则描述',
                                `goods_run_days` int(5) DEFAULT NULL COMMENT '几天内',
                                `sales` int(8) DEFAULT NULL COMMENT '最低单数',
                                `shop_limit` tinyint(1) DEFAULT '0' COMMENT '0 - 不限制商家 1 - 限制商家',
                                `selected_nums` bigint(8) NOT NULL DEFAULT '0',
                                `sign_up_nums` bigint(8) NOT NULL DEFAULT '0',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10166 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `new_activity_config` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                       `activity_id` bigint(20) DEFAULT NULL COMMENT '活动id',
                                       `name` varchar(255) DEFAULT NULL COMMENT 'banner按钮展示名称',
                                       `tag_url` varchar(255) DEFAULT NULL COMMENT '活动名图',
                                       `banner_img` varchar(255) DEFAULT NULL COMMENT 'banner图',
                                       `home_page_img` varchar(255) DEFAULT NULL COMMENT '入口底图',
                                       `detail_img` varchar(255) DEFAULT NULL COMMENT '商详底图',
                                       `label_img` varchar(255) DEFAULT NULL COMMENT '标签图',
                                       `is_show` int(2) NOT NULL DEFAULT '0' COMMENT '是否展示',
                                       `front_cate_col` varchar(255) DEFAULT NULL COMMENT '前台类目集合(,分割)',
                                       `create_time` datetime DEFAULT NULL,
                                       `update_time` datetime DEFAULT NULL,
                                       `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
                                       `number` int(8) DEFAULT NULL COMMENT '首页插入商品数量',
                                       `activity_name` varchar(255) DEFAULT NULL COMMENT '活动名称',
                                       `country` varchar(255) DEFAULT NULL COMMENT '活动国家',
                                       `freight_id` bigint(20) DEFAULT '0' COMMENT '运费模板id',
                                       `freight_name` varchar(255) DEFAULT NULL COMMENT '运费名称',
                                       `is_del` int(2) DEFAULT '0' COMMENT '是否删除  1 删除  0 未删除',
                                       `vir_goods_items_id` bigint(17) DEFAULT NULL COMMENT '同步的虚拟商品列表id',
                                       `label_tag_id` bigint(17) DEFAULT NULL COMMENT '标签图标签id',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `new_activity_goods` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `activity_id` bigint(20) NOT NULL COMMENT '活动Id',
                                      `activity_name` varchar(255) DEFAULT NULL COMMENT '活动名称',
                                      `main_image` varchar(255) DEFAULT NULL COMMENT '活动商品主图',
                                      `goods_id` int(20) DEFAULT NULL COMMENT '商品id',
                                      `goods_name` varchar(1000) DEFAULT NULL,
                                      `category_id` bigint(11) DEFAULT NULL COMMENT '品类id',
                                      `shop_id` int(20) DEFAULT NULL COMMENT '商户Id',
                                      `shop_name` varchar(255) DEFAULT NULL COMMENT '商户名字',
                                      `discount` decimal(3,2) DEFAULT NULL COMMENT '折扣',
                                      `apply_channel` tinyint(1) DEFAULT '0' COMMENT '报名渠道 0:商家报名 1:运营报名',
                                      `create_time` datetime DEFAULT NULL COMMENT '报名时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '操作时间',
                                      `operator` varchar(255) DEFAULT NULL COMMENT '店铺名称',
                                      `sort` int(64) NOT NULL DEFAULT '0' COMMENT '排序',
                                      `status` int(4) NOT NULL DEFAULT '-1' COMMENT '审核状态 1入选  0 落选  -1 待审核',
                                      `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价',
                                      `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价',
                                      `after_discount_min_price` decimal(10,2) DEFAULT NULL COMMENT '折后最低价',
                                      `after_discount_max_price` decimal(10,2) DEFAULT NULL COMMENT '折后最高价',
                                      `after_discount_min_groupon_price` decimal(10,2) DEFAULT NULL COMMENT '折后拼团最低价',
                                      `after_discount_max_groupon_price` decimal(10,2) DEFAULT NULL COMMENT '折后拼团最高价',
                                      `first_auditor` varchar(200) DEFAULT NULL COMMENT '初审审核人',
                                      `first_audit_time` datetime DEFAULT NULL COMMENT '初审时间',
                                      `audit_remark` varchar(200) DEFAULT NULL COMMENT '终审说明',
                                      `auditor` varchar(255) DEFAULT NULL COMMENT '终审人',
                                      `audit_time` datetime DEFAULT NULL COMMENT '终审时间',
                                      `first_status` int(8) DEFAULT '-1' COMMENT '初审状态  1入选  0 落选  -1 待审核',
                                      `first_audit_remark` varchar(200) DEFAULT NULL COMMENT '初审备注',
                                      `sku_increase_after_discount` double NOT NULL DEFAULT '0',
                                      `discount_type` tinyint(4) NOT NULL DEFAULT '0',
                                      `need_modify_price` tinyint(1) DEFAULT '1' COMMENT '是否需要改价 1 - 需要 0 - 不需要',
                                      `auto_select_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 - 未通过 1 - 通过',
                                      `auto_select_fail_reason` varchar(200) DEFAULT NULL COMMENT '机审未通过原因',
                                      `auto_select_time` timestamp NULL DEFAULT NULL COMMENT '机审时间',
                                      `goods_create_time` timestamp NULL DEFAULT NULL COMMENT '商品创建时间',
                                      `is_show` varchar(20) DEFAULT NULL COMMENT '商品状态',
                                      `is_del` tinyint(1) DEFAULT '0' COMMENT '0 - 未删除 1 - 删除',
                                      `is_backend_upload` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是后台上传，0 否 1 是',
                                      `principal` varchar(255) DEFAULT NULL COMMENT '小二名字',
                                      `effect_start_time` datetime DEFAULT '2023-01-01 00:00:00' COMMENT '开始时间',
                                      `effect_end_time` datetime DEFAULT '2099-12-31 00:00:00' COMMENT '结束时间',
                                      `final_price` text COMMENT '最终价格 json 每个国家的价格',
                                      `sku_info` text COMMENT 'sku售价信息',
                                      `country_freight_info` text COMMENT '国家运费信息',
                                      `application_type` tinyint(1) DEFAULT NULL COMMENT '申请类型 1 涨价申请 2 降价申请 3 退出活动',
                                      `approval_status` tinyint(1) DEFAULT NULL COMMENT '审批状态状态（0 待审批 1 通过 2 驳回 3撤销)',
                                      PRIMARY KEY (`id`),
                                      KEY `goods_id_idx` (`goods_id`),
                                      KEY `activity_id_idx` (`activity_id`),
                                      KEY `fail_reason_idx` (`auto_select_fail_reason`),
                                      KEY `idx_principal` (`principal`)
) ENGINE=InnoDB AUTO_INCREMENT=36083 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `new_activity_shop_limit_record` (
                                                  `id` bigint(18) NOT NULL AUTO_INCREMENT,
                                                  `activity_id` bigint(18) NOT NULL,
                                                  `shop_id` bigint(18) NOT NULL,
                                                  `limit_num` int(5) NOT NULL DEFAULT '0',
                                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `uk_activity_shop` (`activity_id`,`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;



// ES MODEL
public class GoodsExtConfigModel implements Serializable {
    private List<Long> tagIds;
    private List<Long> shopTagIds;
}

// ES MODEL
public class GoodsESModelVo implements Serializable {
    private Long id;
    private String itemNumber;
    private String name;
    private String orginalName;
    private Integer type;
    private String isMultiBrand;
    private String code;
    private String areaCode;
    private String isShow;
    private LocalDateTime createTime;
    private Integer status;
    private Integer isDel;
    private BigDecimal minPrice;
    private BigDecimal minMarketPrice;
    private BigDecimal maxPrice;
    private BigDecimal maxMarketPrice;
    private BigDecimal minGrouponPrice;
    private BigDecimal maxGrouponPrice;
    private String mainImage;
    private String originalName;
    private Integer translated;
    private Long categoryId;
    private Long productId;
    private BigDecimal orginalMinPrice;
    private BigDecimal orginalMinMarketPrice;
    private BigDecimal orginalMaxPrice;
    private BigDecimal orginalMaxMarketPrice;
    private Long labelId;
    private String labelName;
    private Long freightTemplateId;
    private Integer isFix;
    private Integer updateType;
    private String tag;
    private Date updateTime;
    private Long sales;
    private Integer channel;
    private Integer appChannel;
    private Long parentId;
    private String country;
    private Long orginalSales;
    private Long score;
    private Long sortValue;
    private Long shopId;
    private String shopName;
    private String weight;
    private String packageSzie;
    private List<GoodsItemESModel> itemList;
    private List<GoodsFreightEsVo> countryDelivery;
    private String propertyValues;
    private List<String> likeName;
    private Double startMinPrice;
    private Double endMinPrice;
    private String orderBy;
    private String sortKey;
    private BigDecimal countryPrice;
    private BigDecimal countryGrouponPrice;
    private List<PropertyESModel> propertyList;
    private Float matchScore;
    private BigDecimal ctr;
    private BigDecimal gcr;
    private Long sizeChartTemplateId;
    private GoodsExtDetailModel goodsExtDetailModel;
    private ListGoodsCommentModel listGoodsCommentModel;
    private BestCouponModel bestCouponModel;
    private GoodsExtConfigModel goodsExtConfigModel;
    private List<GoodsRankingListTagModel> goodsRankingListTagModels;
    private String brandName;
    private BigDecimal vatRate;
    private List<GoodsTagModel> tagList;
    private Long listingId;
    private Long logisticsProperty;
    private Integer deliveryType;
}

